// Sales Order Components component for MRP Dashboard
export class SalesOrderComponents {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.components = [];
    this.filteredComponents = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'InventoryID';
    this.sortDirection = 'asc';
    this.isLoading = false;
  }

  async init() {
    console.log("Initializing Sales Order Components");
    
    this.isLoading = true;
    this.render();
    
    try {
      // Extract components from parent's sales orders data
      await this.extractComponents();
      
      this.isLoading = false;
      this.render();
    } catch (error) {
      console.error("Error initializing components:", error);
      this.isLoading = false;
      this.showError("Failed to initialize components: " + error.message);
      this.render();
    }
  }

  async extractComponents() {
    try {
      // Get sales orders from parent component
      const salesOrders = this.parentComponent.salesOrders || [];
      console.log(`Extracting components from ${salesOrders.length} sales orders`);
      
      const componentsMap = new Map();
      
      salesOrders.forEach(order => {
        if (Array.isArray(order.LineItems)) {
          order.LineItems.forEach(lineItem => {
            if (Array.isArray(lineItem.BOMItems) && lineItem.BOMItems.length > 0) {
              lineItem.BOMItems.forEach(bomItem => {
                const key = bomItem.InventoryID || bomItem.ProductionNbr || 'N/A';
                
                if (componentsMap.has(key)) {
                  const existing = componentsMap.get(key);
                  existing.usageCount++;
                  existing.salesOrders.add(order.OrderNbr);
                  existing.totalQuantity += lineItem.Quantity || 0;
                } else {
                  componentsMap.set(key, {
                    id: `comp-${key}-${Date.now()}`,
                    InventoryID: bomItem.InventoryID || '',
                    ProductionNbr: bomItem.ProductionNbr || '',
                    DisplayID: key,
                    usageCount: 1,
                    salesOrders: new Set([order.OrderNbr]),
                    totalQuantity: lineItem.Quantity || 0,
                    note: bomItem.note || '',
                    rowNumber: bomItem.rowNumber || 0
                  });
                }
              });
            }
          });
        }
      });
      
      // Convert map to array and process
      this.components = Array.from(componentsMap.values()).map(comp => ({
        ...comp,
        salesOrders: Array.from(comp.salesOrders),
        salesOrderCount: comp.salesOrders.size
      }));
      
      console.log(`Extracted ${this.components.length} unique components`);
      
      this.filteredComponents = [...this.components];
      this.calculateTotalPages();
      this.applyFilters();
      
    } catch (error) {
      console.error("Error extracting components:", error);
      this.components = [];
      this.filteredComponents = [];
    }
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredComponents.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.components];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(comp => {
        return (
          comp.DisplayID.toLowerCase().includes(searchLower) ||
          comp.InventoryID.toLowerCase().includes(searchLower) ||
          comp.ProductionNbr.toLowerCase().includes(searchLower) ||
          comp.note.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortField) {
        case 'DisplayID':
          aValue = a.DisplayID;
          bValue = b.DisplayID;
          break;
        case 'usageCount':
          aValue = a.usageCount;
          bValue = b.usageCount;
          break;
        case 'salesOrderCount':
          aValue = a.salesOrderCount;
          bValue = b.salesOrderCount;
          break;
        case 'totalQuantity':
          aValue = a.totalQuantity;
          bValue = b.totalQuantity;
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredComponents = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;
    
    tableContainer.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading components data...</p>
      </div>
    `;
  }

  renderContent() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;
    
    tableContainer.innerHTML = `
      <!-- Components Search and Controls -->
      <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <div class="relative mb-4 md:mb-0">
          <input
            type="text"
            id="components-search"
            placeholder="Search components..."
            class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
            value="${this.searchTerm || ''}"
          >
          <button id="clear-components-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Components Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="DisplayID">
                Component ID <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="usageCount">
                Usage Count <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="salesOrderCount">
                Sales Orders <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="totalQuantity">
                Total Qty <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Notes
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${this.renderTableRows()}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredComponents.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredComponents.length)} of
          ${this.filteredComponents.length} components
        </div>

        <div class="flex items-center space-x-1">
          <button id="components-first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="components-prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>

          <button id="components-next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="components-last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;

    this.setupComponentsEventListeners();
  }

  renderTableRows() {
    if (this.filteredComponents.length === 0) {
      return `
        <tr>
          <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
            No components found
          </td>
        </tr>
      `;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredComponents.length);
    const pageComponents = this.filteredComponents.slice(startIndex, endIndex);

    return pageComponents.map(component => {
      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
          <td class="px-3 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              ${this.escapeHtml(component.DisplayID)}
            </div>
            ${component.ProductionNbr ? `
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Production: ${this.escapeHtml(component.ProductionNbr)}
              </div>
            ` : ''}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-center">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
              ${component.usageCount}
            </span>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-center">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
              ${component.salesOrderCount}
            </span>
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
            ${component.totalQuantity.toFixed(2)}
          </td>
          <td class="px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
            ${this.escapeHtml(component.note || '-')}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button class="view-component-details text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" data-component-id="${component.id}">
              View Details
            </button>
          </td>
        </tr>
      `;
    }).join('');
  }

  setupComponentsEventListeners() {
    // Search input
    const searchInput = document.getElementById('components-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = document.getElementById('clear-components-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Pagination buttons
    const firstPageBtn = document.getElementById('components-first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.render();
      });
    }

    const prevPageBtn = document.getElementById('components-prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = document.getElementById('components-next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = document.getElementById('components-last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        this.currentPage = this.totalPages;
        this.render();
      });
    }

    // View details buttons
    const viewButtons = document.querySelectorAll('.view-component-details');
    viewButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const componentId = e.target.getAttribute('data-component-id');
        this.showComponentDetails(componentId);
      });
    });
  }

  showComponentDetails(componentId) {
    const component = this.components.find(c => c.id === componentId);
    if (!component) return;

    // Create modal content
    const modalContent = `
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Component Details: ${this.escapeHtml(component.DisplayID)}
          </h3>
          <button id="component-details-close" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Component ID</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">${this.escapeHtml(component.DisplayID)}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Production Number</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">${this.escapeHtml(component.ProductionNbr || 'N/A')}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Usage Count</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">${component.usageCount}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Total Quantity</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-white">${component.totalQuantity.toFixed(2)}</p>
          </div>
        </div>

        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Used in Sales Orders</label>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div class="flex flex-wrap gap-2">
              ${component.salesOrders.map(orderNbr => `
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                  ${this.escapeHtml(orderNbr)}
                </span>
              `).join('')}
            </div>
          </div>
        </div>

        ${component.note ? `
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
            <p class="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              ${this.escapeHtml(component.note)}
            </p>
          </div>
        ` : ''}

        <div class="flex justify-end">
          <button id="component-details-close-btn" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
        </div>
      </div>
    `;

    // Create modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.innerHTML = modalContent;

    // Add event listeners
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };

    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) closeModal();
    });

    const closeBtn = modalOverlay.querySelector('#component-details-close');
    const closeBtnBottom = modalOverlay.querySelector('#component-details-close-btn');

    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (closeBtnBottom) closeBtnBottom.addEventListener('click', closeModal);

    document.body.appendChild(modalOverlay);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  escapeHtml(text) {
    if (!text) return '';

    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  showError(message) {
    console.error("Components error:", message);

    const tableContainer = document.getElementById('sales-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }
}
